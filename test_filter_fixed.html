<!DOCTYPE html>
<html>
<head>
    <title>Filter Test - FIXED</title>
    <style>
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <h2>Filter Test - Gaming Tables (FIXED VERSION)</h2>
    
    <input type="text" id="filterInput" placeholder="Filter games by name (min 3 chars)..." style="width: 100%; padding: 10px; margin-bottom: 20px;">
    
    <h3>Games Table (Working)</h3>
    <table>
        <thead>
            <tr>
                <th>Plataforma</th>
                <th>Icon1</th>
                <th>Icon2</th>
                <th>Juego</th>
            </tr>
        </thead>
        <tbody id="gamesTableBody">
            <tr>
                <td>Steam</td>
                <td>🖥️</td>
                <td>⌨️</td>
                <td>Counter Strike</td>
            </tr>
            <tr>
                <td>Epic</td>
                <td></td>
                <td>🎮</td>
                <td>Fortnite</td>
            </tr>
            <tr>
                <td>Steam</td>
                <td>🖥️</td>
                <td>⌨️</td>
                <td>Dota 2</td>
            </tr>
        </tbody>
    </table>
    
    <h3>Pending Games Table (Now Working!)</h3>
    <table>
        <thead>
            <tr>
                <th>Plataforma</th>
                <th>Icon</th>
                <th>Juego</th>
            </tr>
        </thead>
        <tbody id="pendingGamesTableBody">
            <tr>
                <td>Steam</td>
                <td>✅</td>
                <td>Cyberpunk 2077</td>
            </tr>
            <tr>
                <td>Epic</td>
                <td></td>
                <td>Grand Theft Auto V</td>
            </tr>
            <tr>
                <td>Steam</td>
                <td>✅</td>
                <td>The Witcher 3</td>
            </tr>
        </tbody>
    </table>

    <script>
        // FIXED implementation
        document.addEventListener('DOMContentLoaded', function () {
            const filterInput = document.getElementById('filterInput');
            const gamesTableBody = document.getElementById('gamesTableBody');
            const pendingGamesTableBody = document.getElementById('pendingGamesTableBody');
            const allGameRows = Array.from(gamesTableBody.querySelectorAll('tr'));
            const allPendingGameRows = Array.from(pendingGamesTableBody.querySelectorAll('tr'));

            filterInput.addEventListener('input', function () {
                const filterText = filterInput.value.toLowerCase().trim();

                const applyFilter = (rows) => {
                    rows.forEach(row => {
                        // FIXED: Using correct cell index - index 3 for games table, index 2 for pending games table
                        const gameNameCell = row.cells[rows === allGameRows ? 3 : 2];
                        if (gameNameCell) {
                            const gameName = gameNameCell.textContent.toLowerCase().trim();
                            console.log('Row:', rows === allGameRows ? 'games' : 'pending', 'Cell content:', gameName);
                            if (filterText.length >= 3) {
                                if (gameName.includes(filterText)) {
                                    row.style.display = ''; // Show row
                                } else {
                                    row.style.display = 'none'; // Hide row
                                }
                            } else {
                                row.style.display = ''; // Show all rows if filter is too short
                            }
                        }
                    });
                };

                applyFilter(allGameRows);
                applyFilter(allPendingGameRows);
            });
        });
    </script>
    
    <div style="margin-top: 30px; padding: 15px; background-color: #e8f5e8; border: 1px solid #4caf50;">
        <h4>Test Instructions:</h4>
        <ol>
            <li>Try typing "Counter" - should show Counter Strike in games table</li>
            <li>Try typing "Cyber" - should show Cyberpunk 2077 in pending games table</li>
            <li>Try typing "Grand" - should show Grand Theft Auto V in pending games table</li>
            <li>Try typing "Witcher" - should show The Witcher 3 in pending games table</li>
        </ol>
        <p><strong>Expected Result:</strong> Both tables should now filter correctly!</p>
    </div>
</body>
</html>