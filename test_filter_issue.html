<!DOCTYPE html>
<html>
<head>
    <title>Filter Test</title>
    <style>
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <h2>Filter Test - Gaming Tables</h2>
    
    <input type="text" id="filterInput" placeholder="Filter games by name (min 3 chars)..." style="width: 100%; padding: 10px; margin-bottom: 20px;">
    
    <h3>Games Table (Working)</h3>
    <table>
        <thead>
            <tr>
                <th>Plataforma</th>
                <th>Icon1</th>
                <th>Icon2</th>
                <th>Juego</th>
            </tr>
        </thead>
        <tbody id="gamesTableBody">
            <tr>
                <td>Steam</td>
                <td>🖥️</td>
                <td>⌨️</td>
                <td>Counter Strike</td>
            </tr>
            <tr>
                <td>Epic</td>
                <td></td>
                <td>🎮</td>
                <td>Fortnite</td>
            </tr>
            <tr>
                <td>Steam</td>
                <td>🖥️</td>
                <td>⌨️</td>
                <td>Dota 2</td>
            </tr>
        </tbody>
    </table>
    
    <h3>Pending Games Table (Not Working)</h3>
    <table>
        <thead>
            <tr>
                <th>Plataforma</th>
                <th>Icon</th>
                <th>Juego</th>
            </tr>
        </thead>
        <tbody id="pendingGamesTableBody">
            <tr>
                <td>Steam</td>
                <td>✅</td>
                <td>Cyberpunk 2077</td>
            </tr>
            <tr>
                <td>Epic</td>
                <td></td>
                <td>Grand Theft Auto V</td>
            </tr>
            <tr>
                <td>Steam</td>
                <td>✅</td>
                <td>The Witcher 3</td>
            </tr>
        </tbody>
    </table>

    <script>
        // Current buggy implementation from agaming.view.php
        document.addEventListener('DOMContentLoaded', function () {
            const filterInput = document.getElementById('filterInput');
            const gamesTableBody = document.getElementById('gamesTableBody');
            const pendingGamesTableBody = document.getElementById('pendingGamesTableBody');
            const allGameRows = Array.from(gamesTableBody.querySelectorAll('tr'));
            const allPendingGameRows = Array.from(pendingGamesTableBody.querySelectorAll('tr'));

            filterInput.addEventListener('input', function () {
                const filterText = filterInput.value.toLowerCase().trim();

                const applyFilter = (rows) => {
                    rows.forEach(row => {
                        // BUG: Using index 1 for pending games, should be index 2
                        const gameNameCell = row.cells[rows === allGameRows ? 3 : 1];
                        if (gameNameCell) {
                            const gameName = gameNameCell.textContent.toLowerCase().trim();
                            console.log('Row:', rows === allGameRows ? 'games' : 'pending', 'Cell content:', gameName);
                            if (filterText.length >= 3) {
                                if (gameName.includes(filterText)) {
                                    row.style.display = ''; // Show row
                                } else {
                                    row.style.display = 'none'; // Hide row
                                }
                            } else {
                                row.style.display = ''; // Show all rows if filter is too short
                            }
                        }
                    });
                };

                applyFilter(allGameRows);
                applyFilter(allPendingGameRows);
            });
        });
    </script>
</body>
</html>