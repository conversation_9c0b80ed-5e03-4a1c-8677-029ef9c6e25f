-- Database update script for adding handwriting recognition functionality to whiteboards
-- This script adds columns to store recognized text data alongside the image data

-- Add columns for text recognition data
ALTER TABLE `whiteboards` 
ADD COLUMN `recognized_text` LONGTEXT NULL COMMENT 'OCR recognized text from the whiteboard',
ADD COLUMN `text_regions` LONGTEXT NULL COMMENT 'JSON data containing text regions and their coordinates',
ADD COLUMN `recognition_confidence` DECIMAL(5,2) NULL COMMENT 'Average confidence score of text recognition (0-100)',
ADD COLUMN `has_text_recognition` BOOLEAN DEFAULT FALSE COMMENT 'Flag indicating if text recognition has been performed',
ADD COLUMN `text_updated_at` TIMESTAMP NULL COMMENT 'Timestamp when text recognition was last updated';

-- Add index for better performance when searching by text
ALTER TABLE `whiteboards` 
ADD INDEX `idx_has_text_recognition` (`has_text_recognition`),
ADD INDEX `idx_text_updated_at` (`text_updated_at`);

-- Add fulltext index for text search (if using MyISAM or InnoDB with MySQL 5.6+)
-- Uncomment the following line if you want to enable full-text search on recognized text
-- ALTER TABLE `whiteboards` ADD FULLTEXT(`recognized_text`);

-- Update existing records to set default values
UPDATE `whiteboards` 
SET `has_text_recognition` = FALSE, 
    `recognition_confidence` = NULL,
    `text_updated_at` = NULL
WHERE `has_text_recognition` IS NULL;
