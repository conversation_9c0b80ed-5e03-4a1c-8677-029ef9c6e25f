<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>Test TRK ID Functionality</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #2c3e50; color: white; padding: 20px; }
        .panel { background-color: #34495e; border-radius: 5px; }
        .panel-heading { background-color: #3498db; padding: 15px; border-radius: 5px 5px 0 0; }
        .cursor-pointer { cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test TRK ID Copy Functionality</h1>
        
        <!-- Simulate the panel from lhits.view.php -->
        <div class="panel panel-inverse mt-3">
            <div class="panel-heading">
                <h4 class="panel-title d-flex justify-content-between align-items-center">
                    <span>Hits de Hoy 
                        <span id="trk-id-display" 
                              class="badge bg-secondary ms-2 cursor-pointer" 
                              onclick="copyTrkId()" 
                              title="Click para copiar"
                              style="font-size: 0.75em; background-color: #6c757d !important; color: white !important;">
                            TEST-TRK-12345
                        </span>
                    </span>
                </h4>
            </div>
        </div>
        
        <div class="mt-3">
            <p>Click on the TRK ID badge above to test the copy functionality.</p>
            <p>You should see a "Copiado" tooltip appear and the value should be copied to your clipboard.</p>
        </div>
    </div>

    <script>
        // Copy TRK ID to clipboard function
        function copyTrkId() {
            const trkIdElement = document.getElementById('trk-id-display');
            const trkIdValue = trkIdElement.textContent.trim();
            
            // Use the modern Clipboard API if available
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(trkIdValue).then(function() {
                    showCopiedTooltip();
                }).catch(function(err) {
                    // Fallback to older method
                    fallbackCopyTextToClipboard(trkIdValue);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(trkIdValue);
            }
        }
        
        // Fallback copy method for older browsers
        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                showCopiedTooltip();
            } catch (err) {
                console.error('Unable to copy to clipboard', err);
            }
            
            document.body.removeChild(textArea);
        }
        
        // Show "Copiado" tooltip
        function showCopiedTooltip() {
            const trkIdElement = document.getElementById('trk-id-display');
            
            // Create tooltip element
            const tooltip = document.createElement('div');
            tooltip.textContent = 'Copiado';
            tooltip.style.cssText = `
                position: absolute;
                background-color: #28a745;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                z-index: 1000;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            
            // Position tooltip above the element
            const rect = trkIdElement.getBoundingClientRect();
            tooltip.style.left = (rect.left + rect.width / 2 - 25) + 'px';
            tooltip.style.top = (rect.top - 30) + 'px';
            
            document.body.appendChild(tooltip);
            
            // Show tooltip with fade in
            setTimeout(() => {
                tooltip.style.opacity = '1';
            }, 10);
            
            // Hide tooltip after 2 seconds
            setTimeout(() => {
                tooltip.style.opacity = '0';
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        document.body.removeChild(tooltip);
                    }
                }, 300);
            }, 2000);
        }
    </script>
</body>
</html>